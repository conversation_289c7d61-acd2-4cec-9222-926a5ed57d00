<script setup lang="ts">
import { computed, h, onMounted, ref } from 'vue'
import { NDropdown, NInput, NScrollbar } from 'naive-ui'
import { useRoute, useRouter } from 'vue-router'
import { usePublicChat } from '../../hooks/usePublicChat'
import { SvgIcon } from '@/components/common'
import { useAppStore, useChatStore } from '@/store'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { debounce } from '@/utils/functions/debounce'
import {
  getBizConversationList,
  getBizGuestConversationList,
  postBizConversationRemove,
  postBizConversationUpdateConversation,
  removeBizGuestConversation,
  updateBizGuestConversation,
} from '@/api'
import { useIconRender } from '@/hooks/useIconRender'
import { streamResponseManager } from '@/utils/streamResponseManager'

const props = defineProps<{
  searchValue: string
}>()

// 定义emit用于调用父组件的handleAdd方法
const emit = defineEmits<{
  (e: 'createConversation'): void
}>()

const { iconRender } = useIconRender()
const { isMobile } = useBasicLayout()

const appStore = useAppStore()
const chatStore = useChatStore()
const route = useRoute()
const { isPublicChat } = usePublicChat()
const router = useRouter()
const postConversationUpdateConversation = isPublicChat
  ? updateBizGuestConversation
  : postBizConversationUpdateConversation

interface ChatHistory extends Chat.History {
  isEditing?: boolean
}

const dataSources = computed(() => {
  const data = chatStore.history as ChatHistory[]
  console.log('data', data.length)

  if (props.searchValue)
    return data.filter(item => !item.isCasualChat && item.title.includes(props.searchValue))
  return data.filter(item => !item.isCasualChat)
})

function formatTime(timestamp?: string): string {
  if (!timestamp) return ''
  const now = new Date()
  const date = new Date(timestamp)
  const diff = now.getTime() - date.getTime()
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const year = date.getFullYear()

  // 如果是今天
  if (now.toDateString() === date.toDateString()) return `${hours}:${minutes}`
  // 如果是昨天
  else if (diff < 86400000 * 2) return `昨天 ${hours}:${minutes}`
  // 如果是今年
  else if (now.getFullYear() === date.getFullYear()) return `${month}/${day} ${hours}:${minutes}`
  // 其他年份
  else return `${year.toString().slice(-2)}/${month}/${day} ${hours}:${minutes}`
}

async function handleSelect({ conversationId }: Chat.History) {
  if (!conversationId || isActive(conversationId)) return

  if (chatStore.active)
    chatStore.updateHistory(chatStore.active, { isEditing: false } as Partial<ChatHistory>)

  await chatStore.setActive(conversationId)

  if (isMobile.value) appStore.setSiderCollapsed(true)
}

async function handleEdit(
  { conversationId, title }: Chat.History,
  isEditing: boolean,
  event?: MouseEvent,
) {
  event?.stopPropagation()

  if (!isEditing) {
    try {
      await postConversationUpdateConversation({
        id: conversationId,
        name: title,
      })
      chatStore.updateHistory(conversationId, { isEditing } as Partial<ChatHistory>)
    } catch (error) {
      console.error('更新对话失败:', error)
    }
  } else {
    chatStore.updateHistory(conversationId, { isEditing } as Partial<ChatHistory>)
  }
}

async function handleDelete(index: number, event?: MouseEvent | TouchEvent) {
  event?.stopPropagation()

  try {
    const conversation = dataSources.value[index]
    const postConversationRemove = isPublicChat
      ? removeBizGuestConversation
      : postBizConversationRemove
    await postConversationRemove({ id: conversation.conversationId })
    chatStore.deleteHistory(index)
    // resolved 调用接口更新一下列表会不会更可靠？
    // 为什么在这里调用fetchConversationList时候，route是undefined
    await fetchConversationList()
    if (isMobile.value) appStore.setSiderCollapsed(true)
  } catch (error) {
    console.error('删除会话失败:', error)
  }
}

const handleDeleteDebounce = debounce(handleDelete, 600)

async function handleEnter(
  { conversationId, title }: Chat.History,
  isEditing: boolean,
  event: KeyboardEvent,
) {
  event?.stopPropagation()
  if (event.key === 'Enter') {
    try {
      await postConversationUpdateConversation({
        id: conversationId,
        name: title,
      })
      chatStore.updateHistory(conversationId, { isEditing } as Partial<ChatHistory>)
    } catch (error) {
      console.error('更新对话失败:', error)
    }
  }
}

function isActive(conversationId?: string) {
  return conversationId && chatStore.active === conversationId
}

function isHover(conversationId?: string) {
  return conversationId && hoveredId.value === conversationId
}

// 检查会话是否正在进行流式响应
function isConversationLoading(conversationId?: string): boolean {
  return conversationId ? streamResponseManager.isLoading(conversationId) : false
}

interface ConversationListResponse {
  rows: Chat.ConversationVo[]
}

const isUpdatingList = ref(false)

// 获取会话列表
async function fetchConversationList() {
  if (isUpdatingList.value) return

  try {
    isUpdatingList.value = true
    // 从URL中获取当前活跃会话ID
    const currentConversationId = route.params.conversationId as string
    const getConversationList = isPublicChat ? getBizGuestConversationList : getBizConversationList
    const response = await getConversationList<{
      rows: Chat.ConversationVo[]
    }>({ isAsc: 'desc' })

    // 安全地处理响应数据
    const data = Array.isArray(response.rows) ? response.rows : []

    if (data.length > 0) {
      // 添加新的历史记录
      // ！前后端目前fieldcode叫法不一致，考虑后面要不要迁就，但是后端目前命名也不够合理，先不处理吧
      // 修改：避免在循环中触发reloadRoute，先添加到临时数组中
      const historyItems: ChatHistory[] = data.map(
        item =>
          ({
            ...item,
            title: item.name || '新对话',
            summary: item.subtitle,
            conversationId: item.id || '',
            isEditing: false,
          }) as ChatHistory,
      )

      // 检查是否是新创建的会话（刚刚通过addHistory添加的）
      const isNewConversation =
        historyItems.length === 1 &&
        chatStore.history.length === 1 &&
        chatStore.history[0].conversationId === historyItems[0].conversationId

      // 如果不是新创建的会话，才进行完整的列表更新
      if (!isNewConversation) {
        // 清空现有历史记录
        // resolved 清除chatStore.history即可，清除chatStore.chat不合理
        chatStore.clearHistoryOnly()

        // 批量添加所有历史记录，避免在循环中多次触发reloadRoute
        chatStore.history.unshift(...historyItems)
      }

      // 获取所有服务器返回的会话ID列表
      const serverConversationIds = data.map(item => item.id).filter(Boolean) as string[]

      // 使用Map优化查找效率
      const conversationMap = new Map(serverConversationIds.map(id => [id, true]))

      // 使用Set优化现有聊天ID的查找效率
      const existingChatIdsSet = new Set(chatStore.chat.map(chat => chat.conversationId))

      // 过滤有效的聊天记录并收集需要新建的会话ID，只遍历一次serverConversationIds
      const validChats: typeof chatStore.chat = []
      const newConversationIds: string[] = []

      // 保留有效的聊天记录
      chatStore.chat.forEach(chat => {
        if (conversationMap.has(chat.conversationId)) {
          validChats.push(chat)
        }
      })

      // 找出需要新建的会话
      serverConversationIds.forEach(id => {
        if (!existingChatIdsSet.has(id)) {
          newConversationIds.push(id)
        }
      })

      // 一次性更新聊天记录数组，减少状态变更次数
      chatStore.chat = validChats

      // 批量创建新会话的聊天数据
      if (newConversationIds.length > 0) {
        const newChats = newConversationIds.map(conversationId => ({
          conversationId,
          data: [],
        }))
        chatStore.chat.unshift(...newChats)
      }

      // 保存状态
      chatStore.recordState()

      // 如果URL中有会话ID，且该会话存在于列表中，则激活它
      if (currentConversationId && data.some(item => item.id === currentConversationId)) {
        // 目前实际效果：被删掉的上一条的id -》 列表第一条的id -》 currentConversationId
        await chatStore.setActive(currentConversationId)
      } else if (historyItems.length > 0 && historyItems[0].conversationId) {
        // 如果没有在URL中找到有效的会话ID，将第一个会话设为活跃状态并只触发一次路由导航
        chatStore.active = historyItems[0].conversationId
        await chatStore.reloadRoute(historyItems[0].conversationId)
      }
      // 否则，如果没有在URL中找到有效的会话ID，保持第一个会话为活跃状态
    } else {
      // resolved 如果当前没有会话，则创建调用父组件的handleAdd创建一个新会话
      if (isPublicChat) {
        router.push({
          name: 'public-agent',
          params: { agentId: route.query.releaseId as string },
        })
      } else {
        emit('createConversation')
      }
    }
  } catch (error) {
    console.error('获取会话列表失败:', error)
    // 避免在控制台显示错误后再次抛出
  } finally {
    isUpdatingList.value = false
  }
}

const isExpanded = ref(true)
const hoveredId = ref<string>('')

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}

const options = computed(() => {
  return [
    {
      label: '编辑',
      key: 'edit',
      icon: iconRender({ icon: 'ri:edit-line' }),
    },
    {
      label: '删除',
      key: 'delete',
      icon: iconRender({ icon: 'ri:delete-bin-line', color: '#F25B37' }),
    },
  ]
})

function renderLabel(option: any) {
  if (option.key === 'delete') return h('span', { style: 'color: #F25B37' }, option.label)
  else return h('span', { style: '' }, option.label)
}

function handleDropdownSelect(key: string, item: Chat.History, index: number) {
  switch (key) {
    case 'edit':
      handleEdit(item, true)
      break
    case 'delete':
      handleDeleteDebounce(index)
      break
  }
}

onMounted(() => {
  fetchConversationList()
})
</script>

<template>
  <div class="flex flex-col h-full">
    <!-- resolved 这个节点是不是应该放外面 -->
    <div class="flex items-center justify-between pt-2 pb-3 px-6 flex-shrink-0">
      <span class="text-[14px] font-medium" style="color: #646a73">我的对话</span>
      <div class="cursor-pointer" @click="toggleExpand">
        <SvgIcon
          style="font-size: 18px; color: #646a73"
          :icon="isExpanded ? 'ri:arrow-down-s-line' : 'ri:arrow-right-s-line'"
        />
      </div>
    </div>

    <NScrollbar class="px-4 flex-1">
      <div class="flex flex-col gap-2 text-sm">
        <template v-if="!dataSources.length && isExpanded">
          <div class="flex flex-col items-center mt-4 text-center text-neutral-300">
            <SvgIcon icon="ri:inbox-line" class="mb-2 text-3xl" />
            <span>{{ searchValue ? '暂无搜索结果' : '暂无数据' }}</span>
          </div>
        </template>
        <template v-else-if="isExpanded">
          <TransitionGroup name="conversation-list" tag="div" class="flex flex-col gap-2" appear>
            <div v-for="(item, index) of dataSources" :key="`conversation-${item.conversationId}`">
              <!-- resolved 增加逻辑进去，isHover(item.conversationId)时候bg-isHover -->
              <a
                class="conversation-item bg-isHover relative flex items-center gap-3 px-3 py-3 break-all rounded-md cursor-pointer group"
                :class="[
                  isActive(item.conversationId)
                    ? ['bg-isActive', 'dark:bg-[#24272e]', 'dark:border-[#4C5CEC]']
                    : '',
                ]"
                @click="handleSelect(item)"
                @mouseenter="hoveredId = item.conversationId || ''"
                @mouseleave="hoveredId = ''"
              >
                <!-- , 'pr-14' -->
                <span
                  style="
                    background-color: #fff;
                    border: 1px solid rgba(195, 204, 249, 0.3);
                    border-radius: 50%;
                    padding: 10px;
                  "
                >
                  <img src="@/assets/session_avatar.png" class="h-[28px] w-[28px]" />
                </span>
                <div class="relative flex-1 overflow-hidden break-all">
                  <div class="flex justify-between text-ellipsis whitespace-nowrap text-base">
                    <NInput
                      v-if="item.isEditing"
                      v-model:value="item.title"
                      autofocus
                      size="tiny"
                      @keypress="handleEnter(item, false, $event)"
                      @blur="handleEdit(item, false, $event as unknown as MouseEvent)"
                      @click.stop
                    />
                    <div v-else class="flex items-center gap-2">
                      <!-- resolved title限制宽度，超出省略号 -->
                      <span class="max-w-[210px] text-ellipsis whitespace-nowrap overflow-hidden">{{
                        item.title
                      }}</span>
                      <!-- 流式响应加载指示器 -->
                      <div v-if="isConversationLoading(item.conversationId)" class="loading-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                      </div>
                    </div>
                  </div>
                  <!-- resolved 这个隐藏时候v-show="!(isActive(item.conversationId) || isHover(item.conversationId))"会导致class="flex items-center"坍缩破坏样式，怎么办 -->
                  <div class="flex items-center h-[18px] mt-1">
                    <div
                      class="w-[150px] text-xs text-gray-400 text-ellipsis whitespace-nowrap"
                      style="overflow: hidden"
                    >
                      {{ item.summary }}
                    </div>
                    <div
                      v-show="!(isActive(item.conversationId) || isHover(item.conversationId))"
                      class="flex-1 text-xs text-gray-400 text-right"
                    >
                      {{ formatTime(item.lastChatTime) }}
                    </div>
                  </div>
                </div>
                <div
                  v-show="isActive(item.conversationId) || isHover(item.conversationId)"
                  @click.stop
                >
                  <NDropdown
                    v-if="!item.isEditing"
                    trigger="click"
                    :options="options"
                    :render-label="renderLabel"
                    @select="(key: string) => handleDropdownSelect(key, item, index)"
                  >
                    <div class="content-right-btn">
                      <SvgIcon icon="uil:ellipsis-v" />
                    </div>
                  </NDropdown>
                </div>
              </a>
            </div>
          </TransitionGroup>
        </template>
      </div>
    </NScrollbar>
  </div>
</template>

<style scoped lang="less">
.conversation-item {
  &:hover {
    background: rgb(240, 242, 248);
  }

  .content-right-btn {
    height: 40px;
    width: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    &:hover {
      background-color: rgb(229, 232, 240);
    }
  }
}

.bg-isActive {
  background: rgb(237, 240, 253) !important;
  .content-right-btn {
    &:hover {
      background-color: rgb(216, 221, 243) !important;
    }
  }
}

// 流式响应加载指示器样式
.loading-dots {
  display: flex;
  align-items: center;
  gap: 2px;

  span {
    width: 4px;
    height: 4px;
    background-color: #4c5cec;
    border-radius: 50%;
    animation: loading-pulse 1.4s infinite ease-in-out;

    &:nth-child(1) {
      animation-delay: -0.32s;
    }

    &:nth-child(2) {
      animation-delay: -0.16s;
    }

    &:nth-child(3) {
      animation-delay: 0s;
    }
  }
}

@keyframes loading-pulse {
  0%,
  80%,
  100% {
    transform: scale(0.7);
    opacity: 0.4;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.conversation-list-move,
.conversation-list-enter-active,
.conversation-list-leave-active {
  transition: all 0.3s ease;
}

.conversation-list-enter-from {
  opacity: 0;
  transform: translateX(-20px);
}

.conversation-list-leave-to {
  opacity: 0;
  transform: translateX(20px);
}

.conversation-list-leave-active {
  position: absolute;
  width: 100%;
}

/* 防止重复动效的样式 */
.conversation-list-appear-from {
  opacity: 1;
  transform: translateX(0);
}

.conversation-list-appear-active {
  transition: none;
}
</style>
