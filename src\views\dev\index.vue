<script setup lang="ts">
import { ref } from 'vue'
import { NButton, NInputNumber, NPopconfirm, NSwitch } from 'naive-ui'
import { useRouter } from 'vue-router'
import ms from '@/utils/message'
import { SvgIcon } from '@/components/common'
import { useChatStore, useSettingStore, useUserStore } from '@/store'
import { t } from '@/locales'
import { postBizConversationRemove } from '@/api'

const router = useRouter()
const userStore = useUserStore()
const chatStore = useChatStore()
const settingStore = useSettingStore()
const clearNumber = ref(1)
const useAgentStorage = ref(!!localStorage.getItem('use_agent_storage'))

async function handleClearHistory() {
  try {
    const num = clearNumber.value
    const history = chatStore.history
    const sortedHistory = [...history].sort((a, b) => {
      const timeA = new Date(a.lastChatTime || 0).getTime()
      const timeB = new Date(b.lastChatTime || 0).getTime()
      return timeA - timeB
    })

    const toDelete = sortedHistory.slice(0, num)

    for (const item of toDelete) {
      await postBizConversationRemove({ id: item.conversationId })
      const index = history.findIndex(h => h.conversationId === item.conversationId)
      if (index !== -1) {
        chatStore.deleteHistory(index)
      }
    }

    ms.success(`成功删除${toDelete.length}条最早的对话`)
  } catch (error) {
    ms.error(`删除失败：${error}`)
  }
}

// resolved 清除数据库倒数历史记录
async function handleClearLatestHistory() {
  try {
    const num = clearNumber.value
    const history = chatStore.history
    const sortedHistory = [...history].sort((a, b) => {
      const timeA = new Date(a.lastChatTime || 0).getTime()
      const timeB = new Date(b.lastChatTime || 0).getTime()
      return timeB - timeA // 倒序排列，最新的在前
    })

    const toDelete = sortedHistory.slice(0, num)

    for (const item of toDelete) {
      await postBizConversationRemove({ id: item.conversationId })
      const index = history.findIndex(h => h.conversationId === item.conversationId)
      if (index !== -1) {
        chatStore.deleteHistory(index)
      }
    }

    ms.success(`成功删除${toDelete.length}条最新的对话`)
  } catch (error) {
    ms.error(`删除失败：${error}`)
  }
}

function handleClearLocalHistory() {
  try {
    chatStore.clearHistory()
    ms.success('成功清除本地所有历史记录')
    router.push('/chat')
  } catch (error) {
    ms.error(`清除失败：${error}`)
  }
}

function handleLogout() {
  userStore.logout()
  ms.success(t('common.success'))
}

function handleSystemMessageChange(value: boolean) {
  chatStore.setUseSystemMessage(value)
  ms.success(value ? '已开启系统提示词，开启期间新创建的对话均展示思维链' : '已关闭系统提示词')
}

function handleAgentStorageChange(value: boolean) {
  if (value) {
    localStorage.setItem('use_agent_storage', 'true')
    ms.success('已启用Agent编辑页的自动保存功能')
  } else {
    localStorage.removeItem('use_agent_storage')
    ms.success('已禁用Agent编辑页的自动保存功能')
  }
  useAgentStorage.value = value
}

function handleTypewriterEffectChange(value: boolean) {
  settingStore.updateSetting({ typewriterEffect: value })
  ms.success(value ? '已启用打字机效果' : '已禁用打字机效果')
}
</script>

<template>
  <div class="h-full p-4">
    <h2 class="text-2xl font-bold mb-4">开发</h2>
    <div class="space-y-6">
      <NPopconfirm placement="bottom" @positive-click="handleLogout">
        <template #trigger>
          <NButton size="large" type="warning">
            <template #icon>
              <SvgIcon icon="ri:logout-box-line" />
            </template>
            退出登录
          </NButton>
        </template>
        {{ $t('common.logoutConfirm') }}
      </NPopconfirm>
      <!-- <div>
        <NButton size="large" type="error" @click="handleClearLocalHistory">
          <template #icon>
            <SvgIcon icon="ri:delete-bin-line" />
          </template>
          清除本地历史记录（总计{{ chatStore.history.length }}条）
        </NButton>
      </div> -->
      <div class="flex items-center space-x-4">
        <NInputNumber
          v-model:value="clearNumber"
          :min="1"
          :max="999"
          class="w-[120px]"
          placeholder="删除数量"
        />
        <NButton size="large" type="error" @click="handleClearHistory">
          <template #icon>
            <SvgIcon icon="ri:delete-bin-line" />
          </template>
          清除数据库正数{{ clearNumber || 0 }}条历史记录（总计{{ chatStore.history.length }}条）
        </NButton>
      </div>
      <div class="flex items-center space-x-4">
        <NInputNumber
          v-model:value="clearNumber"
          :min="1"
          :max="999"
          class="w-[120px]"
          placeholder="删除数量"
        />
        <NButton size="large" type="error" @click="handleClearHistory">
          <template #icon>
            <SvgIcon icon="ri:delete-bin-line" />
          </template>
          清除数据库倒数{{ clearNumber || 0 }}条历史记录（总计{{ chatStore.history.length }}条）
        </NButton>
      </div>
      <div class="flex items-center space-x-4">
        <span class="text-base">新建会话时使用系统提示词强制开启思维链:</span>
        <NSwitch :value="chatStore.useSystemMessage" @update:value="handleSystemMessageChange" />
      </div>
      <div class="flex items-center space-x-4">
        <span class="text-base">启用Agent编辑页自动保存(开发中):</span>
        <!-- 切换后生效了，但是switch状态没有及时变化 -->
        <NSwitch :value="useAgentStorage" @update:value="handleAgentStorageChange" />
      </div>
      <div class="flex items-center space-x-4">
        <span class="text-base">启用打字机效果:</span>
        <NSwitch
          :value="settingStore.typewriterEffect"
          @update:value="handleTypewriterEffectChange"
        />
      </div>
      <!-- resolved 放个switch按钮，能够开启关闭打字机效果，typewriterEffect -->
    </div>
  </div>
</template>
